{"name": "branchqa", "version": "1.0.0", "description": "AI-powered QA test case generator from GitHub branch comparison", "main": "src/index.js", "bin": {"branchqa": "./bin/branchqa.js"}, "directories": {"doc": "docs", "test": "tests"}, "scripts": {"start": "node src/index.js", "dev": "node --watch src/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["qa", "testing", "github", "ai", "test-cases", "branch-comparison", "automation"], "author": "BranchQA Team", "license": "MIT", "engines": {"node": ">=16.0.0"}, "dependencies": {"axios": "^1.11.0", "commander": "^14.0.0", "openai": "^5.10.2", "parse-diff": "^0.11.1", "simple-git": "^3.28.0"}, "devDependencies": {"eslint": "^9.31.0", "jest": "^30.0.5"}}