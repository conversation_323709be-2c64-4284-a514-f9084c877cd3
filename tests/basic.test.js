/**
 * Basic tests for BranchQA
 */

const { BranchQA } = require('../src/index');
const { DiffAnalyzer } = require('../src/core/DiffAnalyzer');
const { ChangeAnalyzer } = require('../src/core/ChangeAnalyzer');
const { ChecklistGenerator } = require('../src/core/ChecklistGenerator');

describe('BranchQA Core Components', () => {
  
  describe('DiffAnalyzer', () => {
    test('should create DiffAnalyzer instance', () => {
      const analyzer = new DiffAnalyzer();
      expect(analyzer).toBeInstanceOf(DiffAnalyzer);
    });

    test('should categorize file types correctly', () => {
      const analyzer = new DiffAnalyzer();
      
      expect(analyzer.categorizeFile('src/components/Header.jsx')).toBe('frontend');
      expect(analyzer.categorizeFile('api/controllers/UserController.php')).toBe('backend');
      expect(analyzer.categorizeFile('config/database.yml')).toBe('config');
      expect(analyzer.categorizeFile('tests/user.test.js')).toBe('tests');
    });

    test('should detect programming languages', () => {
      const analyzer = new DiffAnalyzer();
      
      expect(analyzer.detectLanguage('app.js')).toBe('JavaScript');
      expect(analyzer.detectLanguage('component.tsx')).toBe('React/TypeScript');
      expect(analyzer.detectLanguage('model.php')).toBe('PHP');
      expect(analyzer.detectLanguage('styles.css')).toBe('CSS');
    });
  });

  describe('ChangeAnalyzer', () => {
    test('should create ChangeAnalyzer instance', () => {
      const analyzer = new ChangeAnalyzer();
      expect(analyzer).toBeInstanceOf(ChangeAnalyzer);
    });

    test('should extract component names', () => {
      const analyzer = new ChangeAnalyzer();
      
      expect(analyzer.extractComponentName('UserProfile.component.js')).toBe('User Profile');
      expect(analyzer.extractComponentName('shopping-cart.js')).toBe('Shopping Cart');
      expect(analyzer.extractComponentName('AuthController.php')).toBe('Auth Controller');
    });
  });

  describe('ChecklistGenerator', () => {
    test('should create ChecklistGenerator instance', () => {
      const generator = new ChecklistGenerator();
      expect(generator).toBeInstanceOf(ChecklistGenerator);
    });

    test('should calculate priority correctly', () => {
      const generator = new ChecklistGenerator();
      
      const highRiskAnalysis = {
        risk_assessment: { level: 'high' }
      };
      
      const lowRiskAnalysis = {
        risk_assessment: { level: 'low' }
      };
      
      expect(generator.calculatePriority(highRiskAnalysis)).toBe('Critical');
      expect(generator.calculatePriority(lowRiskAnalysis)).toBe('Medium');
    });

    test('should generate test routes', () => {
      const generator = new ChecklistGenerator();
      
      const analysis = {
        impact: {
          pages: ['User Profile', 'Settings']
        }
      };
      
      const routes = generator.generateTestRoutes('authentication', analysis);
      expect(routes).toContain('/user-profile');
      expect(routes).toContain('/settings');
    });
  });

  describe('BranchQA Main Class', () => {
    test('should create BranchQA instance', () => {
      const branchQA = new BranchQA();
      expect(branchQA).toBeInstanceOf(BranchQA);
    });

    test('should initialize with options', () => {
      const options = {
        repo: 'test/repo',
        base: 'main',
        head: 'feature',
        verbose: true
      };
      
      const branchQA = new BranchQA(options);
      expect(branchQA.options).toEqual(options);
    });
  });
});

describe('Integration Tests', () => {
  test('should handle missing dependencies gracefully', () => {
    // Test that the app doesn't crash when dependencies are missing
    expect(() => {
      const branchQA = new BranchQA();
    }).not.toThrow();
  });
});

// Mock data for testing
const mockDiffData = {
  text: `diff --git a/src/components/Header.jsx b/src/components/Header.jsx
index 1234567..abcdefg 100644
--- a/src/components/Header.jsx
+++ b/src/components/Header.jsx
@@ -1,5 +1,8 @@
 import React from 'react';
 
+const Header = () => {
+  return <header>New Header</header>;
+};
+
 export default Header;`,
  summary: {
    files: [
      {
        file: 'src/components/Header.jsx',
        additions: 3,
        deletions: 0
      }
    ]
  }
};

const mockAnalysis = {
  summary: {
    title: 'Header Component Update',
    description: 'Updated header component with new styling'
  },
  what: {
    components: ['Header Component'],
    files: ['src/components/Header.jsx'],
    scope: 'Frontend component modification'
  },
  why: {
    purpose: 'Improve user interface',
    problem_solved: 'Outdated header design',
    user_benefit: 'Better visual experience'
  },
  impact: {
    user_workflows: ['Navigation'],
    admin_features: [],
    integrations: [],
    performance: 'Minimal impact',
    security: 'No security implications'
  },
  risk_assessment: {
    level: 'low',
    areas: ['Frontend display'],
    edge_cases: ['Mobile responsiveness']
  }
};
