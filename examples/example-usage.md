# BranchQA Usage Examples

## Basic Usage

### 1. Initialize BranchQA
```bash
# Set up GitHub token
branchqa init --token YOUR_GITHUB_TOKEN

# Or use environment variable
export GITHUB_TOKEN=your_github_token
export OPENAI_API_KEY=your_openai_api_key
```

### 2. Analyze Branch Differences
```bash
# Compare feature branch with main
branchqa analyze \
  --repo owner/repository \
  --base main \
  --head feature/new-authentication \
  --output ./qa-checklist.md
```

### 3. Advanced Usage
```bash
# Use custom template and verbose output
branchqa analyze \
  --repo mycompany/webapp \
  --base develop \
  --head feature/payment-integration \
  --output ./output/payment-qa.md \
  --template ./templates/ecommerce.md \
  --verbose
```

## Example Scenarios

### WordPress Plugin Development
```bash
branchqa analyze \
  --repo mycompany/wp-plugin \
  --base main \
  --head feature/gutenberg-blocks \
  --output ./qa/gutenberg-testing.md
```

### React Application
```bash
branchqa analyze \
  --repo frontend-team/react-app \
  --base main \
  --head feature/user-dashboard \
  --output ./testing/dashboard-qa.md
```

### API Development
```bash
branchqa analyze \
  --repo backend-team/api-service \
  --base main \
  --head feature/v2-endpoints \
  --output ./qa/api-v2-testing.md
```

## Configuration Management

### Check Current Configuration
```bash
branchqa config
```

### Environment Variables
```bash
# GitHub token (required)
export GITHUB_TOKEN=ghp_xxxxxxxxxxxxxxxxxxxx

# OpenAI API key (required)
export OPENAI_API_KEY=sk-xxxxxxxxxxxxxxxxxxxx

# Optional: Default output directory
export BRANCHQA_OUTPUT_DIR=./qa-reports
```

## Output Examples

The generated QA checklist will include:

- **Summary** of changes and business impact
- **Test Cases** with step-by-step instructions
- **Edge Cases** and error scenarios
- **Compatibility Testing** across browsers and devices
- **Performance Testing** considerations
- **Security Testing** requirements

## Tips for Best Results

1. **Meaningful Commit Messages**: Write clear, descriptive commit messages for better AI analysis
2. **Pull Request Descriptions**: Include detailed PR descriptions explaining the "why" behind changes
3. **Consistent File Organization**: Use clear file and folder naming conventions
4. **Regular Updates**: Run BranchQA on smaller, focused feature branches for more targeted testing

## Troubleshooting

### Common Issues

1. **GitHub Token Issues**
   ```bash
   # Verify token has correct permissions
   curl -H "Authorization: token YOUR_TOKEN" https://api.github.com/user
   ```

2. **Repository Access**
   ```bash
   # Ensure token has access to private repositories if needed
   # Token needs 'repo' scope for private repos
   ```

3. **OpenAI API Limits**
   ```bash
   # Check API usage and limits
   # Consider using GPT-3.5-turbo for cost optimization
   ```
