{"name": "jest-haste-map", "version": "30.0.5", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-haste-map"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "require": "./build/index.js", "import": "./build/index.mjs", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@jest/types": "30.0.5", "@types/node": "*", "anymatch": "^3.1.3", "fb-watchman": "^2.0.2", "graceful-fs": "^4.2.11", "jest-regex-util": "30.0.1", "jest-util": "30.0.5", "jest-worker": "30.0.5", "micromatch": "^4.0.8", "walker": "^1.0.8"}, "devDependencies": {"@types/fb-watchman": "^2.0.5", "@types/graceful-fs": "^4.1.9", "@types/micromatch": "^4.0.9", "slash": "^3.0.0"}, "optionalDependencies": {"fsevents": "^2.3.3"}, "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "22236cf58b66039f81893537c90dee290bab427f"}