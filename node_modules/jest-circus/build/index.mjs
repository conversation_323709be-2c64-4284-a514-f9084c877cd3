import cjsModule from './index.js';

export const addEventHandler = cjsModule.addEventHandler;
export const afterAll = cjsModule.afterAll;
export const afterEach = cjsModule.afterEach;
export const beforeAll = cjsModule.beforeAll;
export const beforeEach = cjsModule.beforeEach;
export const describe = cjsModule.describe;
export const getState = cjsModule.getState;
export const it = cjsModule.it;
export const removeEventHandler = cjsModule.removeEventHandler;
export const resetState = cjsModule.resetState;
export const run = cjsModule.run;
export const setState = cjsModule.setState;
export const test = cjsModule.test;
export default cjsModule.default;
