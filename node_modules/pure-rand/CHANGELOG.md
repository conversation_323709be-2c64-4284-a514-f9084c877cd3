# CHANGELOG 7.X

## 7.0.1

### Fixes

- [c24bc93](https://github.com/dubzzz/pure-rand/commit/c24bc93) 🐛 Properly define exports in package.json (#758)

## 7.0.0

### Breaking Changes

- [2c94832](https://github.com/dubzzz/pure-rand/commit/2c94832) 🏷️ Move to "import type" when feasible (#736)
- [3741a63](https://github.com/dubzzz/pure-rand/commit/3741a63) 🏷️ Mark `getState` as compulsory on `RandomGenerator` (#733)

### Features

- [228c73d](https://github.com/dubzzz/pure-rand/commit/228c73d) ⚡️ Faster uniform distributions on bigint (#757)
- [86869a1](https://github.com/dubzzz/pure-rand/commit/86869a1) ✨ Expose generators and distributions (#735)

### Fixes

- [680a672](https://github.com/dubzzz/pure-rand/commit/680a672) 🚚 Do not export mersenne as default (#738)
- [e1758c0](https://github.com/dubzzz/pure-rand/commit/e1758c0) 🚚 Split ArrayInt into two files (#737)
- [0c356cf](https://github.com/dubzzz/pure-rand/commit/0c356cf) 🚚 Moving files around (#734)
- [6d9b7b4](https://github.com/dubzzz/pure-rand/commit/6d9b7b4) 📝 Document generation of float/double (#715)
