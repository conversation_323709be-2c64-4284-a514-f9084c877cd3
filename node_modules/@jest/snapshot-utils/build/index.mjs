import cjsModule from './index.js';

export const SNAPSHOT_GUIDE_LINK = cjsModule.SNAPSHOT_GUIDE_LINK;
export const SNAPSHOT_VERSION = cjsModule.SNAPSHOT_VERSION;
export const SNAPSHOT_VERSION_WARNING = cjsModule.SNAPSHOT_VERSION_WARNING;
export const ensureDirectoryExists = cjsModule.ensureDirectoryExists;
export const escapeBacktickString = cjsModule.escapeBacktickString;
export const getSnapshotData = cjsModule.getSnapshotData;
export const keyToTestName = cjsModule.keyToTestName;
export const normalizeNewlines = cjsModule.normalizeNewlines;
export const saveSnapshotFile = cjsModule.saveSnapshotFile;
export const testNameToKey = cjsModule.testNameToKey;
