{"name": "@jest/globals", "version": "30.0.5", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-globals"}, "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@jest/environment": "30.0.5", "@jest/expect": "30.0.5", "@jest/types": "30.0.5", "jest-mock": "30.0.5"}, "publishConfig": {"access": "public"}, "gitHead": "22236cf58b66039f81893537c90dee290bab427f"}