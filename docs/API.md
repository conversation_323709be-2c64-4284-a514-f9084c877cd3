# BranchQA API Documentation

## Core Classes

### BranchQA

Main application class that orchestrates the analysis workflow.

```javascript
const { BranchQA } = require('branchqa');

const branchQA = new BranchQA(options);
await branchQA.analyze();
```

#### Constructor Options

| Option | Type | Description | Default |
|--------|------|-------------|---------|
| `repo` | string | GitHub repository (owner/repo) | - |
| `base` | string | Base branch for comparison | - |
| `head` | string | Head branch for comparison | - |
| `output` | string | Output file path | `./output/qa-checklist.md` |
| `token` | string | GitHub access token | `process.env.GITHUB_TOKEN` |
| `template` | string | Custom template file path | - |
| `format` | string | Output format (markdown, html) | `markdown` |
| `verbose` | boolean | Enable verbose logging | `false` |

#### Methods

##### `async analyze()`
Performs the complete analysis workflow:
1. Initialize services
2. Get branch differences
3. Analyze code changes
4. Assess impact
5. Generate AI context
6. Create QA checklist
7. Save output

##### `async init(options)`
Initialize BranchQA configuration.

##### `async showConfig()`
Display current configuration.

---

### GitHubService

Handles GitHub API interactions and git operations.

```javascript
const { GitHubService } = require('branchqa/src/services/GitHubService');

const github = new GitHubService(token);
const diffData = await github.getBranchDiff(repo, base, head);
```

#### Methods

##### `async getBranchDiff(repository, baseBranch, headBranch)`
Get comprehensive diff data between two branches.

**Returns:**
```javascript
{
  repository: Object,      // Repository information
  baseBranch: string,      // Base branch name
  headBranch: string,      // Head branch name
  diff: Object,           // Parsed diff data
  commits: Array,         // Commit information
  pullRequest: Object,    // PR information (if available)
  stats: Object          // Diff statistics
}
```

##### `async validateToken()`
Validate GitHub token and return user information.

---

### DiffAnalyzer

Parses git diffs and categorizes changes.

```javascript
const { DiffAnalyzer } = require('branchqa/src/core/DiffAnalyzer');

const analyzer = new DiffAnalyzer();
const analysis = await analyzer.analyzeDiff(diffData);
```

#### Methods

##### `async analyzeDiff(diffData)`
Analyze git diff and extract meaningful information.

**Returns:**
```javascript
{
  files: Array,              // File-level analysis
  categories: Object,        // Categorized changes
  functions: Array,          // Function-level changes
  significantChanges: Array, // Notable changes
  stats: Object,            // Overall statistics
  riskAssessment: Object    // Risk analysis
}
```

##### `categorizeFile(filePath)`
Categorize file by type (frontend, backend, config, etc.).

##### `detectLanguage(filePath)`
Detect programming language from file extension.

---

### ChangeAnalyzer

Analyzes code changes and determines business impact.

```javascript
const { ChangeAnalyzer } = require('branchqa/src/core/ChangeAnalyzer');

const analyzer = new ChangeAnalyzer();
const impact = await analyzer.analyzeImpact(parsedChanges);
```

#### Methods

##### `async analyzeImpact(parsedChanges)`
Analyze the impact of code changes.

**Returns:**
```javascript
{
  components: Array,        // Affected components
  userWorkflows: Array,     // Affected user workflows
  businessFunctions: Array, // Business functions impacted
  integrations: Array,      // Affected integrations
  userRoles: Array,        // User roles affected
  pages: Array,            // Pages/routes affected
  riskAreas: Object,       // Risk areas by level
  testingPriority: string, // Testing priority level
  userFacing: boolean,     // Has user-facing changes
  backend: boolean,        // Has backend changes
  breaking: boolean        // Potentially breaking changes
}
```

---

### AIService

Handles OpenAI API interactions for contextual analysis.

```javascript
const { AIService } = require('branchqa/src/services/AIService');

const ai = new AIService(apiKey);
const context = await ai.generateContext(parsedChanges, impactAnalysis, commits);
```

#### Methods

##### `async generateContext(parsedChanges, impactAnalysis, commits)`
Generate contextual analysis using AI.

**Returns:**
```javascript
{
  summary: {
    title: string,
    description: string
  },
  what: {
    components: Array,
    files: Array,
    scope: string
  },
  why: {
    purpose: string,
    problem_solved: string,
    user_benefit: string
  },
  impact: {
    user_workflows: Array,
    admin_features: Array,
    integrations: Array,
    performance: string,
    security: string
  },
  risk_assessment: {
    level: string,
    areas: Array,
    edge_cases: Array
  }
}
```

##### `async generateTestScenarios(contextualAnalysis, additionalContext)`
Generate test scenarios based on analysis.

---

### ChecklistGenerator

Generates comprehensive QA test checklists.

```javascript
const { ChecklistGenerator } = require('branchqa/src/core/ChecklistGenerator');

const generator = new ChecklistGenerator();
const checklist = await generator.generate(contextualAnalysis, templatePath);
```

#### Methods

##### `async generate(contextualAnalysis, templatePath)`
Generate QA checklist from contextual analysis.

**Returns:** Markdown-formatted checklist string.

##### `generateTestCases(analysis)`
Generate main test cases section.

##### `generateEdgeCases(analysis)`
Generate edge cases and error scenarios.

##### `generateCompatibilityTests(analysis)`
Generate browser/device compatibility tests.

---

## Configuration Classes

### ConfigManager

Manages BranchQA configuration and credentials.

```javascript
const { ConfigManager } = require('branchqa/src/utils/ConfigManager');

const config = new ConfigManager();
await config.setGitHubToken(token);
```

#### Methods

##### `async getConfig()`
Get complete configuration including environment variables.

##### `async setGitHubToken(token)`
Store GitHub token securely.

##### `async getOpenAIKey()`
Retrieve OpenAI API key.

---

### Logger

Utility class for consistent logging.

```javascript
const { Logger } = require('branchqa/src/utils/Logger');

const logger = new Logger(verbose);
logger.info('Processing...');
```

#### Methods

- `info(message)` - Information messages
- `success(message)` - Success messages  
- `warn(message)` - Warning messages
- `error(message)` - Error messages
- `debug(message)` - Debug messages (verbose mode only)

---

## Data Structures

### Diff Data Structure

```javascript
{
  text: string,           // Raw diff text
  summary: {
    files: Array,         // File summaries
    insertions: number,   // Total insertions
    deletions: number     // Total deletions
  }
}
```

### File Analysis Structure

```javascript
{
  path: string,           // File path
  type: string,           // Change type (added, modified, deleted)
  category: string,       // File category (frontend, backend, etc.)
  additions: number,      // Lines added
  deletions: number,      // Lines deleted
  chunks: number,         // Number of change chunks
  binary: boolean,        // Is binary file
  language: string,       // Programming language
  complexity: string      // Complexity level (low, medium, high)
}
```

### Risk Assessment Structure

```javascript
{
  level: string,          // Risk level (low, medium, high)
  score: number,          // Numeric risk score
  factors: Array          // Risk factors identified
}
```

---

## Error Handling

All methods throw descriptive errors that can be caught and handled:

```javascript
try {
  await branchQA.analyze();
} catch (error) {
  console.error('Analysis failed:', error.message);
  
  // Common error types:
  // - GitHub authentication errors
  // - Repository access errors
  // - OpenAI API errors
  // - File system errors
}
```

---

## Extension Points

### Custom Templates

Create custom checklist templates:

```javascript
const generator = new ChecklistGenerator();
const checklist = await generator.generate(analysis, './custom-template.md');
```

### Custom Analysis

Extend the analysis pipeline:

```javascript
class CustomAnalyzer extends ChangeAnalyzer {
  async analyzeImpact(parsedChanges) {
    const baseImpact = await super.analyzeImpact(parsedChanges);
    // Add custom analysis logic
    return { ...baseImpact, customField: 'value' };
  }
}
```
