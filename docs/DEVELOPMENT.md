# BranchQA Development Guide

## Project Overview

BranchQA is an AI-powered QA test case generator that analyzes GitHub branch differences and creates comprehensive testing checklists. The application is built with Node.js and follows a modular architecture.

## Architecture

### Core Components

```
src/
├── core/                   # Core analysis engines
│   ├── DiffAnalyzer.js    # Git diff parsing and analysis
│   ├── ChangeAnalyzer.js  # Impact assessment logic
│   └── ChecklistGenerator.js # QA checklist generation
├── services/              # External service integrations
│   ├── GitHubService.js   # GitHub API interactions
│   └── AIService.js       # OpenAI API integration
├── utils/                 # Utility functions
│   ├── ConfigManager.js   # Configuration management
│   └── Logger.js          # Logging utility
├── templates/             # QA checklist templates
└── index.js              # Main application class
```

### Data Flow

1. **Input**: Repository, base branch, head branch
2. **GitHub Service**: Fetch repository data and git diff
3. **Diff Analyzer**: Parse and categorize code changes
4. **Change Analyzer**: Assess business impact and risk
5. **AI Service**: Generate contextual analysis
6. **Checklist Generator**: Create comprehensive QA checklist
7. **Output**: Markdown file with testing instructions

## Development Setup

### Prerequisites

- Node.js 16.0.0+
- npm or yarn
- Git
- GitHub account with API access
- OpenAI API key

### Installation

```bash
# Clone repository
git clone https://github.com/branchqa/branchqa.git
cd branchqa

# Install dependencies
npm install

# Set up environment variables
export GITHUB_TOKEN=your_github_token
export OPENAI_API_KEY=your_openai_key

# Run tests
npm test

# Test CLI
node bin/branchqa.js --help
```

### Development Workflow

1. **Feature Development**
   ```bash
   # Create feature branch
   git checkout -b feature/new-feature
   
   # Make changes
   # Write tests
   # Update documentation
   
   # Run tests
   npm test
   
   # Test CLI functionality
   node bin/branchqa.js analyze --repo test/repo --base main --head feature
   ```

2. **Testing**
   ```bash
   # Run all tests
   npm test
   
   # Run tests in watch mode
   npm run test:watch
   
   # Run linting
   npm run lint
   
   # Fix linting issues
   npm run lint:fix
   ```

3. **Documentation**
   ```bash
   # Update relevant documentation
   # - README.md for user-facing changes
   # - API.md for API changes
   # - DEVELOPMENT.md for development changes
   ```

## Code Style

### JavaScript Standards

- Use ES6+ features
- Prefer `const` and `let` over `var`
- Use async/await for asynchronous operations
- Follow JSDoc commenting standards
- Use descriptive variable and function names

### File Organization

- One class per file
- Use PascalCase for class names
- Use camelCase for function and variable names
- Group related functionality in modules
- Keep files under 300 lines when possible

### Error Handling

```javascript
// Always use try-catch for async operations
try {
  const result = await someAsyncOperation();
  return result;
} catch (error) {
  throw new Error(`Operation failed: ${error.message}`);
}

// Provide meaningful error messages
throw new Error('GitHub token is required. Use --token option or set GITHUB_TOKEN environment variable');
```

## Testing Strategy

### Unit Tests

- Test individual functions and methods
- Mock external dependencies (GitHub API, OpenAI API)
- Focus on business logic and edge cases
- Aim for >80% code coverage

### Integration Tests

- Test component interactions
- Use real data structures
- Test CLI commands end-to-end
- Validate output formats

### Test Structure

```javascript
describe('ComponentName', () => {
  beforeEach(() => {
    // Setup
  });

  test('should handle normal case', () => {
    // Test implementation
  });

  test('should handle edge case', () => {
    // Test implementation
  });

  test('should handle error case', () => {
    // Test implementation
  });
});
```

## API Integration

### GitHub API

- Use personal access tokens for authentication
- Implement rate limiting and retry logic
- Cache repository data when possible
- Handle private repository access

### OpenAI API

- Use structured prompts for consistent results
- Implement fallback for API failures
- Monitor token usage and costs
- Handle rate limiting gracefully

## Performance Considerations

### Repository Handling

- Clone repositories to temporary directories
- Use shallow clones for large repositories
- Clean up temporary files after analysis
- Cache repository data between runs

### Memory Management

- Stream large diff files when possible
- Limit AI prompt sizes to avoid token limits
- Clean up resources after processing
- Monitor memory usage for large repositories

## Security

### Token Management

- Store tokens securely in user config directory
- Never log or expose tokens in output
- Validate token permissions before use
- Support token rotation

### Data Privacy

- Process code locally when possible
- Inform users about OpenAI data processing
- Provide options to exclude sensitive files
- Respect repository access permissions

## Deployment

### NPM Package

```bash
# Build for distribution
npm run build

# Test package locally
npm pack
npm install -g branchqa-1.0.0.tgz

# Publish to NPM
npm publish
```

### GitHub Releases

- Tag releases with semantic versioning
- Include changelog in release notes
- Provide installation instructions
- Include example usage

## Contributing

### Pull Request Process

1. Fork the repository
2. Create a feature branch
3. Make changes with tests
4. Update documentation
5. Submit pull request
6. Address review feedback

### Code Review Checklist

- [ ] Code follows style guidelines
- [ ] Tests are included and passing
- [ ] Documentation is updated
- [ ] No breaking changes (or properly documented)
- [ ] Security considerations addressed
- [ ] Performance impact assessed

## Troubleshooting

### Common Development Issues

1. **API Rate Limits**
   - Implement exponential backoff
   - Use conditional requests when possible
   - Monitor API usage

2. **Large Repository Handling**
   - Use streaming for large diffs
   - Implement timeout handling
   - Provide progress indicators

3. **Cross-Platform Compatibility**
   - Test on Windows, macOS, and Linux
   - Handle path separators correctly
   - Use cross-platform dependencies

### Debug Mode

Enable verbose logging for development:

```bash
node bin/branchqa.js analyze --verbose --repo test/repo --base main --head feature
```

## Future Enhancements

### Planned Features

- GitLab and Bitbucket support
- Custom template engine
- Web-based interface
- CI/CD integration
- Historical analysis
- Team collaboration features

### Architecture Improvements

- Plugin system for extensibility
- Database storage for analysis history
- Real-time collaboration features
- Advanced caching strategies
- Microservices architecture for scaling
