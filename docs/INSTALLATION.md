# BranchQA Installation Guide

## Prerequisites

- **Node.js** 16.0.0 or higher
- **npm** or **yarn** package manager
- **Git** installed and configured
- **GitHub account** with repository access
- **OpenAI API key** (for AI-powered analysis)

## Installation Methods

### Method 1: Global Installation (Recommended)

```bash
# Install globally via npm
npm install -g branchqa

# Verify installation
branchqa --version
```

### Method 2: Local Development Setup

```bash
# Clone the repository
git clone https://github.com/branchqa/branchqa.git
cd branchqa

# Install dependencies
npm install

# Make CLI executable
chmod +x bin/branchqa.js

# Test installation
node bin/branchqa.js --version
```

### Method 3: Using npx (No Installation)

```bash
# Run directly with npx
npx branchqa analyze --repo owner/repo --base main --head feature
```

## Configuration

### 1. GitHub Authentication

BranchQA requires a GitHub personal access token to access repositories.

#### Create GitHub Token:
1. Go to GitHub Settings → Developer settings → Personal access tokens
2. Click "Generate new token (classic)"
3. Select scopes:
   - `repo` (for private repositories)
   - `public_repo` (for public repositories)
4. Copy the generated token

#### Set Token:
```bash
# Method 1: Using BranchQA init command
branchqa init --token YOUR_GITHUB_TOKEN

# Method 2: Environment variable
export GITHUB_TOKEN=your_github_token_here

# Method 3: Pass as command argument
branchqa analyze --token YOUR_TOKEN --repo owner/repo --base main --head feature
```

### 2. OpenAI API Configuration

#### Get OpenAI API Key:
1. Visit [OpenAI API](https://platform.openai.com/api-keys)
2. Create a new API key
3. Copy the key (starts with `sk-`)

#### Set API Key:
```bash
# Environment variable (recommended)
export OPENAI_API_KEY=sk-your-openai-api-key

# Add to your shell profile for persistence
echo 'export OPENAI_API_KEY=sk-your-openai-api-key' >> ~/.bashrc
source ~/.bashrc
```

### 3. Verify Configuration

```bash
# Check current configuration
branchqa config

# Expected output:
# Current Configuration:
# - GitHub Token: ✅ Set
# - OpenAI API Key: ✅ Set
```

## Quick Start

### 1. Basic Usage

```bash
# Analyze differences between main and feature branch
branchqa analyze \
  --repo owner/repository \
  --base main \
  --head feature/new-feature \
  --output ./qa-checklist.md
```

### 2. Example with Real Repository

```bash
# Example: Analyze a public repository
branchqa analyze \
  --repo facebook/react \
  --base main \
  --head feature-branch \
  --output ./react-qa.md \
  --verbose
```

## Directory Structure

After installation, BranchQA creates the following structure:

```
~/.branchqa/           # Configuration directory
├── config.json        # User configuration
└── templates/         # Custom templates (optional)

./output/              # Default output directory
└── qa-checklist.md    # Generated QA checklists
```

## Troubleshooting

### Common Issues

#### 1. Permission Denied
```bash
# Fix: Make CLI executable
chmod +x bin/branchqa.js

# Or run with node
node bin/branchqa.js --help
```

#### 2. GitHub Token Issues
```bash
# Test token validity
curl -H "Authorization: token YOUR_TOKEN" https://api.github.com/user

# Common issues:
# - Token expired
# - Insufficient permissions
# - Wrong token format
```

#### 3. OpenAI API Issues
```bash
# Check API key format (should start with 'sk-')
echo $OPENAI_API_KEY

# Common issues:
# - Invalid API key
# - Insufficient credits
# - Rate limiting
```

#### 4. Repository Access
```bash
# For private repositories, ensure token has 'repo' scope
# For organizations, token may need additional permissions
```

#### 5. Network Issues
```bash
# Check internet connectivity
ping api.github.com
ping api.openai.com

# Check proxy settings if behind corporate firewall
```

### Debug Mode

Enable verbose logging for troubleshooting:

```bash
branchqa analyze \
  --repo owner/repo \
  --base main \
  --head feature \
  --verbose
```

### Log Files

BranchQA logs are written to:
- Console output (with `--verbose` flag)
- System logs (platform-specific)

## Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `GITHUB_TOKEN` | GitHub personal access token | Yes |
| `OPENAI_API_KEY` | OpenAI API key | Yes |
| `BRANCHQA_OUTPUT_DIR` | Default output directory | No |
| `BRANCHQA_TEMPLATE_DIR` | Custom templates directory | No |

## Performance Optimization

### 1. Repository Caching
BranchQA caches repositories in `/tmp/branchqa-repos/` to speed up subsequent analyses.

### 2. API Rate Limits
- GitHub API: 5,000 requests/hour (authenticated)
- OpenAI API: Varies by plan

### 3. Large Repositories
For large repositories:
- Use specific branch comparisons
- Consider using shallow clones
- Monitor API usage

## Security Considerations

1. **Token Storage**: Tokens are stored locally in `~/.branchqa/config.json`
2. **API Keys**: Never commit API keys to version control
3. **Repository Access**: Only request necessary permissions
4. **Data Privacy**: Code is processed by OpenAI API (review their privacy policy)

## Next Steps

1. Read the [Usage Examples](../examples/example-usage.md)
2. Explore [Custom Templates](./TEMPLATES.md)
3. Check out [Advanced Configuration](./CONFIGURATION.md)
4. Join our [Community](https://github.com/branchqa/branchqa/discussions)
