/**
 * Diff Analyzer for BranchQA
 * Parses git diffs and categorizes changes
 */

const parseDiff = require('parse-diff');
const path = require('path');

class DiffAnalyzer {
  constructor() {
    this.fileTypeCategories = {
      frontend: ['.js', '.jsx', '.ts', '.tsx', '.vue', '.html', '.css', '.scss', '.sass', '.less'],
      backend: ['.php', '.py', '.rb', '.java', '.go', '.rs', '.cs', '.cpp', '.c'],
      config: ['.json', '.yml', '.yaml', '.xml', '.ini', '.env', '.config'],
      database: ['.sql', '.migration', '.schema'],
      assets: ['.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.woff', '.woff2', '.ttf'],
      documentation: ['.md', '.txt', '.rst', '.adoc'],
      tests: ['.test.js', '.spec.js', '.test.ts', '.spec.ts', '_test.py', '_spec.py']
    };
  }

  /**
   * Analyze git diff and extract meaningful information
   */
  async analyzeDiff(diffData) {
    try {
      const parsedDiff = parseDiff(diffData.text);
      
      const analysis = {
        files: this.analyzeFiles(parsedDiff),
        categories: this.categorizeChanges(parsedDiff),
        functions: this.extractFunctionChanges(parsedDiff),
        significantChanges: this.identifySignificantChanges(parsedDiff),
        stats: this.calculateStats(parsedDiff),
        riskAssessment: this.assessRisk(parsedDiff)
      };

      return analysis;
    } catch (error) {
      throw new Error(`Failed to analyze diff: ${error.message}`);
    }
  }

  /**
   * Analyze individual files in the diff
   */
  analyzeFiles(parsedDiff) {
    return parsedDiff.map(file => {
      const fileInfo = {
        path: file.to || file.from,
        type: this.getChangeType(file),
        category: this.categorizeFile(file.to || file.from),
        additions: file.additions || 0,
        deletions: file.deletions || 0,
        chunks: file.chunks?.length || 0,
        binary: file.binary || false
      };

      // Add language detection
      fileInfo.language = this.detectLanguage(fileInfo.path);
      
      // Add complexity assessment
      fileInfo.complexity = this.assessFileComplexity(file);

      return fileInfo;
    });
  }

  /**
   * Get the type of change for a file
   */
  getChangeType(file) {
    if (file.new) return 'added';
    if (file.deleted) return 'deleted';
    if (file.renamed) return 'renamed';
    return 'modified';
  }

  /**
   * Categorize file by type
   */
  categorizeFile(filePath) {
    if (!filePath) return 'unknown';
    
    const ext = path.extname(filePath).toLowerCase();
    const fileName = path.basename(filePath).toLowerCase();
    
    // Check for test files first
    if (fileName.includes('test') || fileName.includes('spec')) {
      return 'tests';
    }
    
    // Check each category
    for (const [category, extensions] of Object.entries(this.fileTypeCategories)) {
      if (extensions.some(extension => 
        ext === extension || fileName.includes(extension.replace('.', ''))
      )) {
        return category;
      }
    }
    
    return 'other';
  }

  /**
   * Detect programming language
   */
  detectLanguage(filePath) {
    if (!filePath) return 'unknown';
    
    const ext = path.extname(filePath).toLowerCase();
    const languageMap = {
      '.js': 'JavaScript',
      '.jsx': 'React/JSX',
      '.ts': 'TypeScript',
      '.tsx': 'React/TypeScript',
      '.php': 'PHP',
      '.py': 'Python',
      '.rb': 'Ruby',
      '.java': 'Java',
      '.go': 'Go',
      '.rs': 'Rust',
      '.cs': 'C#',
      '.cpp': 'C++',
      '.c': 'C',
      '.html': 'HTML',
      '.css': 'CSS',
      '.scss': 'SCSS',
      '.sass': 'Sass',
      '.vue': 'Vue.js',
      '.sql': 'SQL',
      '.json': 'JSON',
      '.yml': 'YAML',
      '.yaml': 'YAML',
      '.xml': 'XML'
    };
    
    return languageMap[ext] || 'Unknown';
  }

  /**
   * Categorize changes by impact area
   */
  categorizeChanges(parsedDiff) {
    const categories = {
      frontend: [],
      backend: [],
      config: [],
      database: [],
      tests: [],
      documentation: [],
      assets: []
    };

    parsedDiff.forEach(file => {
      const category = this.categorizeFile(file.to || file.from);
      if (categories[category]) {
        categories[category].push({
          file: file.to || file.from,
          type: this.getChangeType(file),
          additions: file.additions || 0,
          deletions: file.deletions || 0
        });
      }
    });

    return categories;
  }

  /**
   * Extract function-level changes
   */
  extractFunctionChanges(parsedDiff) {
    const functionChanges = [];

    parsedDiff.forEach(file => {
      if (!file.chunks) return;

      file.chunks.forEach(chunk => {
        chunk.changes.forEach(change => {
          if (change.type === 'add' || change.type === 'del') {
            const functionInfo = this.extractFunctionInfo(change.content, file.to || file.from);
            if (functionInfo) {
              functionChanges.push({
                file: file.to || file.from,
                function: functionInfo.name,
                type: change.type === 'add' ? 'added' : 'removed',
                line: change.ln || change.ln2,
                context: functionInfo.context
              });
            }
          }
        });
      });
    });

    return functionChanges;
  }

  /**
   * Extract function information from code line
   */
  extractFunctionInfo(line, filePath) {
    const ext = path.extname(filePath).toLowerCase();
    
    // JavaScript/TypeScript function patterns
    if (['.js', '.jsx', '.ts', '.tsx'].includes(ext)) {
      const patterns = [
        /function\s+(\w+)\s*\(/,
        /(\w+)\s*:\s*function\s*\(/,
        /(\w+)\s*=\s*\([^)]*\)\s*=>/,
        /(\w+)\s*\([^)]*\)\s*{/,
        /class\s+(\w+)/,
        /(\w+)\s*\([^)]*\)\s*:/
      ];
      
      for (const pattern of patterns) {
        const match = line.match(pattern);
        if (match) {
          return {
            name: match[1],
            context: line.trim()
          };
        }
      }
    }
    
    // PHP function patterns
    if (ext === '.php') {
      const patterns = [
        /function\s+(\w+)\s*\(/,
        /class\s+(\w+)/,
        /(public|private|protected)\s+function\s+(\w+)\s*\(/
      ];
      
      for (const pattern of patterns) {
        const match = line.match(pattern);
        if (match) {
          return {
            name: match[2] || match[1],
            context: line.trim()
          };
        }
      }
    }
    
    // Python function patterns
    if (ext === '.py') {
      const patterns = [
        /def\s+(\w+)\s*\(/,
        /class\s+(\w+)/
      ];
      
      for (const pattern of patterns) {
        const match = line.match(pattern);
        if (match) {
          return {
            name: match[1],
            context: line.trim()
          };
        }
      }
    }
    
    return null;
  }

  /**
   * Identify significant changes that need special attention
   */
  identifySignificantChanges(parsedDiff) {
    const significantChanges = [];

    parsedDiff.forEach(file => {
      const fileInfo = {
        file: file.to || file.from,
        category: this.categorizeFile(file.to || file.from),
        type: this.getChangeType(file),
        additions: file.additions || 0,
        deletions: file.deletions || 0
      };

      // Large files (>100 lines changed)
      if ((fileInfo.additions + fileInfo.deletions) > 100) {
        significantChanges.push({
          ...fileInfo,
          reason: 'Large change',
          snippet: this.extractSignificantSnippet(file)
        });
      }

      // New files
      if (fileInfo.type === 'added') {
        significantChanges.push({
          ...fileInfo,
          reason: 'New file',
          snippet: this.extractSignificantSnippet(file)
        });
      }

      // Deleted files
      if (fileInfo.type === 'deleted') {
        significantChanges.push({
          ...fileInfo,
          reason: 'File deleted',
          snippet: null
        });
      }

      // Configuration changes
      if (fileInfo.category === 'config') {
        significantChanges.push({
          ...fileInfo,
          reason: 'Configuration change',
          snippet: this.extractSignificantSnippet(file)
        });
      }
    });

    return significantChanges;
  }

  /**
   * Extract a significant snippet from file changes
   */
  extractSignificantSnippet(file) {
    if (!file.chunks || file.chunks.length === 0) return null;

    const snippet = [];
    const maxLines = 10;
    let lineCount = 0;

    for (const chunk of file.chunks) {
      for (const change of chunk.changes) {
        if (lineCount >= maxLines) break;
        if (change.type === 'add' || change.type === 'del') {
          snippet.push(`${change.type === 'add' ? '+' : '-'} ${change.content}`);
          lineCount++;
        }
      }
      if (lineCount >= maxLines) break;
    }

    return snippet.join('\n');
  }

  /**
   * Calculate overall statistics
   */
  calculateStats(parsedDiff) {
    const stats = {
      totalFiles: parsedDiff.length,
      totalAdditions: 0,
      totalDeletions: 0,
      newFiles: 0,
      deletedFiles: 0,
      modifiedFiles: 0,
      renamedFiles: 0,
      binaryFiles: 0
    };

    parsedDiff.forEach(file => {
      stats.totalAdditions += file.additions || 0;
      stats.totalDeletions += file.deletions || 0;

      if (file.new) stats.newFiles++;
      else if (file.deleted) stats.deletedFiles++;
      else if (file.renamed) stats.renamedFiles++;
      else stats.modifiedFiles++;

      if (file.binary) stats.binaryFiles++;
    });

    return stats;
  }

  /**
   * Assess file complexity
   */
  assessFileComplexity(file) {
    const totalChanges = (file.additions || 0) + (file.deletions || 0);
    const chunks = file.chunks?.length || 0;

    if (totalChanges > 200 || chunks > 10) return 'high';
    if (totalChanges > 50 || chunks > 5) return 'medium';
    return 'low';
  }

  /**
   * Assess overall risk of changes
   */
  assessRisk(parsedDiff) {
    let riskScore = 0;
    const riskFactors = [];

    parsedDiff.forEach(file => {
      const category = this.categorizeFile(file.to || file.from);
      const totalChanges = (file.additions || 0) + (file.deletions || 0);

      // Risk factors
      if (category === 'config') {
        riskScore += 3;
        riskFactors.push('Configuration changes');
      }
      if (category === 'database') {
        riskScore += 4;
        riskFactors.push('Database changes');
      }
      if (file.new) {
        riskScore += 2;
        riskFactors.push('New files');
      }
      if (file.deleted) {
        riskScore += 3;
        riskFactors.push('Deleted files');
      }
      if (totalChanges > 100) {
        riskScore += 2;
        riskFactors.push('Large changes');
      }
    });

    let level = 'low';
    if (riskScore > 10) level = 'high';
    else if (riskScore > 5) level = 'medium';

    return {
      level,
      score: riskScore,
      factors: [...new Set(riskFactors)]
    };
  }
}

module.exports = { DiffAnalyzer };
