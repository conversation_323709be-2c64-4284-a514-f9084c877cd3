/**
 * Checklist Generator for BranchQA
 * Generates comprehensive QA test checklists in Markdown format
 */

const fs = require('fs').promises;
const path = require('path');

class ChecklistGenerator {
  constructor() {
    this.defaultTemplate = this.getDefaultTemplate();
    this.browsers = ['Chrome', 'Firefox', 'Safari', 'Edge'];
    this.devices = ['Desktop', 'Tablet', 'Mobile'];
    this.userRoles = ['Administrator', 'Editor', 'Author', 'Subscriber', 'Guest'];
  }

  /**
   * Generate QA checklist from contextual analysis
   */
  async generate(contextualAnalysis, templatePath = null) {
    try {
      const template = templatePath ? await this.loadTemplate(templatePath) : this.defaultTemplate;
      
      const checklist = this.buildChecklist(contextualAnalysis, template);
      
      return checklist;
    } catch (error) {
      throw new Error(`Failed to generate checklist: ${error.message}`);
    }
  }

  /**
   * Build the complete checklist
   */
  buildChecklist(analysis, template) {
    const sections = {
      header: this.generateHeader(analysis),
      summary: this.generateSummary(analysis),
      testCases: this.generateTestCases(analysis),
      edgeCases: this.generateEdgeCases(analysis),
      compatibility: this.generateCompatibilityTests(analysis),
      performance: this.generatePerformanceTests(analysis),
      security: this.generateSecurityTests(analysis),
      footer: this.generateFooter(analysis)
    };

    return this.assembleChecklist(sections, template);
  }

  /**
   * Generate checklist header
   */
  generateHeader(analysis) {
    const date = new Date().toISOString().split('T')[0];
    const title = analysis.summary?.title || 'QA Test Plan';
    
    return `# QA Test Plan: ${title}

**Generated:** ${date}  
**Risk Level:** ${analysis.risk_assessment?.level || 'medium'}  
**Priority:** ${this.calculatePriority(analysis)}

---
`;
  }

  /**
   * Generate summary section
   */
  generateSummary(analysis) {
    const summary = analysis.summary || {};
    const what = analysis.what || {};
    const why = analysis.why || {};
    const impact = analysis.impact || {};

    return `## 📋 Summary

**What Changed:** ${summary.description || 'Code modifications requiring testing'}

**Components Affected:** ${what.components?.join(', ') || 'Various components'}

**Business Purpose:** ${why.purpose || 'Feature enhancement or bug fix'}

**User Impact:** ${impact.user_workflows?.join(', ') || 'User interface and functionality'}

**Files Modified:** ${what.files?.length || 0} files

---
`;
  }

  /**
   * Generate main test cases
   */
  generateTestCases(analysis) {
    let testCases = '## ✅ Test Cases\n\n';

    const components = analysis.what?.components || ['General Functionality'];
    const userWorkflows = analysis.impact?.user_workflows || ['Basic functionality'];
    const userRoles = analysis.impact?.user_roles || ['All users'];

    components.forEach(component => {
      testCases += this.generateComponentTestCase(component, analysis, userWorkflows, userRoles);
    });

    return testCases;
  }

  /**
   * Generate test case for a specific component
   */
  generateComponentTestCase(component, analysis, userWorkflows, userRoles) {
    const routes = this.generateTestRoutes(component, analysis);
    const steps = this.generateTestSteps(component, userWorkflows);
    const expectedResults = this.generateExpectedResults(component, analysis);

    return `### ${component}

- [ ] **${component}** - ${this.getComponentDescription(component, analysis)}
  - **Routes to test:** ${routes.join(', ')}
  - **User roles:** ${userRoles.slice(0, 3).join(', ')}
  - **Compatibility:** ${this.browsers.slice(0, 2).join(', ')}
  - **Testing steps:**
${steps.map((step, index) => `    ${index + 1}. ${step}`).join('\n')}
  - **Expected results:** ${expectedResults}
  - **Edge cases:** ${this.generateComponentEdgeCases(component)}

`;
  }

  /**
   * Get component description
   */
  getComponentDescription(component, analysis) {
    const descriptions = {
      'react': 'React component functionality',
      'vue': 'Vue.js component behavior',
      'controllers': 'Backend controller logic',
      'models': 'Data model operations',
      'api': 'API endpoint functionality',
      'authentication': 'User authentication flow',
      'navigation': 'Site navigation features',
      'forms': 'Form submission and validation',
      'search': 'Search functionality',
      'ecommerce': 'E-commerce features'
    };

    return descriptions[component.toLowerCase()] || `${component} functionality`;
  }

  /**
   * Generate test routes
   */
  generateTestRoutes(component, analysis) {
    const pages = analysis.impact?.pages || [];
    
    if (pages.length > 0) {
      return pages.map(page => `/${page.toLowerCase().replace(/\s+/g, '-')}`);
    }

    // Generate default routes based on component
    const routeMap = {
      'authentication': ['/login', '/register', '/profile'],
      'navigation': ['/', '/about', '/contact'],
      'forms': ['/contact', '/register', '/checkout'],
      'search': ['/search', '/products'],
      'ecommerce': ['/products', '/cart', '/checkout'],
      'admin': ['/admin', '/dashboard'],
      'api': ['/api/*']
    };

    return routeMap[component.toLowerCase()] || ['/'];
  }

  /**
   * Generate test steps
   */
  generateTestSteps(component, userWorkflows) {
    const stepTemplates = {
      'authentication': [
        'Navigate to login page',
        'Enter valid credentials',
        'Click login button',
        'Verify successful authentication',
        'Test logout functionality'
      ],
      'navigation': [
        'Load the homepage',
        'Click on navigation menu items',
        'Verify page loads correctly',
        'Test responsive navigation on mobile'
      ],
      'forms': [
        'Navigate to form page',
        'Fill in required fields',
        'Submit the form',
        'Verify form submission success',
        'Test form validation'
      ],
      'search': [
        'Navigate to search page',
        'Enter search query',
        'Execute search',
        'Verify search results',
        'Test search filters'
      ]
    };

    return stepTemplates[component.toLowerCase()] || [
      'Navigate to relevant page',
      'Perform the main action',
      'Verify expected behavior',
      'Test error scenarios'
    ];
  }

  /**
   * Generate expected results
   */
  generateExpectedResults(component, analysis) {
    const resultTemplates = {
      'authentication': 'User successfully logs in and is redirected to dashboard',
      'navigation': 'All navigation links work correctly and pages load without errors',
      'forms': 'Form submits successfully with proper validation and feedback',
      'search': 'Search returns relevant results with proper pagination',
      'api': 'API endpoints return correct data with proper status codes'
    };

    return resultTemplates[component.toLowerCase()] || 'Functionality works as expected without errors';
  }

  /**
   * Generate component-specific edge cases
   */
  generateComponentEdgeCases(component) {
    const edgeCaseMap = {
      'authentication': 'Invalid credentials, expired sessions, password reset',
      'navigation': 'Broken links, deep linking, browser back/forward',
      'forms': 'Empty fields, invalid data, special characters',
      'search': 'Empty queries, no results, special characters',
      'api': 'Invalid parameters, rate limiting, timeout scenarios'
    };

    return edgeCaseMap[component.toLowerCase()] || 'Boundary conditions, error states, invalid inputs';
  }

  /**
   * Generate edge cases section
   */
  generateEdgeCases(analysis) {
    const edgeCases = analysis.risk_assessment?.edge_cases || [
      'Invalid input data',
      'Network connectivity issues',
      'Browser compatibility',
      'Mobile responsiveness'
    ];

    let section = '## 🔍 Edge Cases & Error Scenarios\n\n';
    
    edgeCases.forEach(edgeCase => {
      section += `- [ ] **${edgeCase}**\n`;
      section += `  - Test with various input combinations\n`;
      section += `  - Verify proper error handling\n`;
      section += `  - Check user feedback messages\n\n`;
    });

    return section;
  }

  /**
   * Generate compatibility tests
   */
  generateCompatibilityTests(analysis) {
    let section = '## 🌐 Compatibility Testing\n\n';
    
    section += '### Browser Testing\n';
    this.browsers.forEach(browser => {
      section += `- [ ] **${browser}** - Test core functionality\n`;
    });
    
    section += '\n### Device Testing\n';
    this.devices.forEach(device => {
      section += `- [ ] **${device}** - Responsive design and functionality\n`;
    });
    
    section += '\n### User Role Testing\n';
    const userRoles = analysis.impact?.user_roles || this.userRoles.slice(0, 3);
    userRoles.forEach(role => {
      section += `- [ ] **${role}** - Role-specific functionality\n`;
    });

    return section + '\n';
  }

  /**
   * Generate performance tests
   */
  generatePerformanceTests(analysis) {
    const performanceImpact = analysis.impact?.performance || 'Standard performance testing required';
    
    return `## ⚡ Performance Testing

- [ ] **Page Load Times** - Verify pages load within acceptable time limits
- [ ] **Resource Loading** - Check CSS, JS, and image loading
- [ ] **Database Queries** - Monitor query performance if backend changes
- [ ] **Memory Usage** - Check for memory leaks in long-running sessions
- [ ] **Mobile Performance** - Test on slower network connections

**Performance Impact:** ${performanceImpact}

`;
  }

  /**
   * Generate security tests
   */
  generateSecurityTests(analysis) {
    const securityImpact = analysis.impact?.security || 'Standard security testing required';
    
    return `## 🔒 Security Testing

- [ ] **Input Validation** - Test for XSS and injection attacks
- [ ] **Authentication** - Verify proper access controls
- [ ] **Authorization** - Test role-based permissions
- [ ] **Data Protection** - Check sensitive data handling
- [ ] **HTTPS** - Verify secure connections

**Security Impact:** ${securityImpact}

`;
  }

  /**
   * Generate footer
   */
  generateFooter(analysis) {
    return `---

## 📝 Notes

- Test on both staging and production environments
- Document any issues found during testing
- Verify fixes before marking items complete
- Consider accessibility testing for user-facing changes

**Generated by BranchQA** - AI-powered QA test case generator`;
  }

  /**
   * Calculate priority level
   */
  calculatePriority(analysis) {
    const riskLevel = analysis.risk_assessment?.level || 'medium';
    const priorityMap = {
      'high': 'Critical',
      'medium': 'High',
      'low': 'Medium'
    };
    
    return priorityMap[riskLevel] || 'Medium';
  }

  /**
   * Assemble the complete checklist
   */
  assembleChecklist(sections, template) {
    // For now, use simple concatenation
    // In the future, this could use a template engine
    return Object.values(sections).join('\n');
  }

  /**
   * Load custom template
   */
  async loadTemplate(templatePath) {
    try {
      const template = await fs.readFile(templatePath, 'utf8');
      return template;
    } catch (error) {
      console.warn(`Could not load template ${templatePath}, using default`);
      return this.defaultTemplate;
    }
  }

  /**
   * Get default template structure
   */
  getDefaultTemplate() {
    return {
      sections: ['header', 'summary', 'testCases', 'edgeCases', 'compatibility', 'performance', 'security', 'footer'],
      format: 'markdown'
    };
  }
}

module.exports = { ChecklistGenerator };
