/**
 * Change Analyzer for BranchQA
 * Analyzes code changes and determines their impact on user workflows and business functionality
 */

const path = require('path');

class ChangeAnalyzer {
  constructor() {
    this.componentPatterns = {
      // Frontend component patterns
      react: [/components?\/.*\.(jsx?|tsx?)$/i, /\.component\.(jsx?|tsx?)$/i],
      vue: [/components?\/.*\.vue$/i, /\.vue$/i],
      angular: [/\.component\.(ts|js)$/i, /components?\/.*\.(ts|js)$/i],
      
      // Backend patterns
      controllers: [/controllers?\/.*\.(php|py|rb|js|ts)$/i, /\.controller\.(php|py|rb|js|ts)$/i],
      models: [/models?\/.*\.(php|py|rb|js|ts)$/i, /\.model\.(php|py|rb|js|ts)$/i],
      services: [/services?\/.*\.(php|py|rb|js|ts)$/i, /\.service\.(php|py|rb|js|ts)$/i],
      
      // WordPress specific
      wordpress: [/wp-content\/themes\/.*\.php$/i, /wp-content\/plugins\/.*\.php$/i, /functions\.php$/i],
      
      // API patterns
      api: [/api\/.*\.(php|py|rb|js|ts)$/i, /routes\/.*\.(php|py|rb|js|ts)$/i],
      
      // Database patterns
      migrations: [/migrations?\/.*\.(php|py|rb|sql)$/i, /\.migration\.(php|py|rb)$/i],
      schemas: [/schema.*\.(sql|js|ts|php|py)$/i, /database\/.*\.(sql|js|ts)$/i]
    };

    this.userWorkflowPatterns = {
      authentication: [/auth|login|register|password|session/i],
      navigation: [/nav|menu|header|footer|sidebar/i],
      forms: [/form|input|submit|validation/i],
      search: [/search|filter|query/i],
      ecommerce: [/cart|checkout|payment|order|product/i],
      content: [/post|page|article|content|editor/i],
      media: [/image|video|audio|gallery|upload/i],
      user_profile: [/profile|account|settings|preferences/i],
      admin: [/admin|dashboard|management|control/i],
      notifications: [/notification|alert|message|email/i]
    };

    this.riskIndicators = {
      high: [
        /security|auth|password|token|session/i,
        /payment|billing|checkout|order/i,
        /database|migration|schema/i,
        /config|settings|environment/i
      ],
      medium: [
        /api|endpoint|route/i,
        /validation|sanitization/i,
        /cache|performance/i,
        /integration|webhook/i
      ],
      low: [
        /style|css|theme/i,
        /documentation|readme/i,
        /test|spec/i,
        /comment|log/i
      ]
    };
  }

  /**
   * Analyze the impact of code changes
   */
  async analyzeImpact(parsedChanges) {
    try {
      const impact = {
        components: this.identifyAffectedComponents(parsedChanges),
        userWorkflows: this.identifyAffectedWorkflows(parsedChanges),
        businessFunctions: this.identifyBusinessFunctions(parsedChanges),
        integrations: this.identifyAffectedIntegrations(parsedChanges),
        userRoles: this.identifyAffectedUserRoles(parsedChanges),
        pages: this.identifyAffectedPages(parsedChanges),
        riskAreas: this.identifyRiskAreas(parsedChanges),
        testingPriority: this.calculateTestingPriority(parsedChanges),
        userFacing: this.isUserFacing(parsedChanges),
        backend: this.isBackendChange(parsedChanges),
        breaking: this.isBreakingChange(parsedChanges)
      };

      return impact;
    } catch (error) {
      throw new Error(`Failed to analyze impact: ${error.message}`);
    }
  }

  /**
   * Identify affected components
   */
  identifyAffectedComponents(parsedChanges) {
    const components = new Set();

    parsedChanges.files.forEach(file => {
      const filePath = file.path;
      
      // Check component patterns
      for (const [componentType, patterns] of Object.entries(this.componentPatterns)) {
        if (patterns.some(pattern => pattern.test(filePath))) {
          components.add(componentType);
        }
      }

      // Extract component names from file paths
      const componentName = this.extractComponentName(filePath);
      if (componentName) {
        components.add(componentName);
      }
    });

    return Array.from(components);
  }

  /**
   * Extract component name from file path
   */
  extractComponentName(filePath) {
    const fileName = path.basename(filePath, path.extname(filePath));
    
    // Remove common prefixes/suffixes
    const cleanName = fileName
      .replace(/\.(component|controller|service|model)$/i, '')
      .replace(/^(component|controller|service|model)\./i, '');
    
    // Convert to readable format
    return cleanName
      .replace(/([A-Z])/g, ' $1')
      .replace(/[-_]/g, ' ')
      .trim()
      .toLowerCase()
      .replace(/\b\w/g, l => l.toUpperCase());
  }

  /**
   * Identify affected user workflows
   */
  identifyAffectedWorkflows(parsedChanges) {
    const workflows = new Set();

    parsedChanges.files.forEach(file => {
      const filePath = file.path.toLowerCase();
      
      for (const [workflow, patterns] of Object.entries(this.userWorkflowPatterns)) {
        if (patterns.some(pattern => pattern.test(filePath))) {
          workflows.add(workflow.replace(/_/g, ' '));
        }
      }

      // Check function changes for workflow indicators
      if (parsedChanges.functions) {
        parsedChanges.functions.forEach(func => {
          if (func.file === file.path) {
            for (const [workflow, patterns] of Object.entries(this.userWorkflowPatterns)) {
              if (patterns.some(pattern => pattern.test(func.function))) {
                workflows.add(workflow.replace(/_/g, ' '));
              }
            }
          }
        });
      }
    });

    return Array.from(workflows);
  }

  /**
   * Identify business functions affected
   */
  identifyBusinessFunctions(parsedChanges) {
    const businessFunctions = new Set();

    // Map technical changes to business functions
    const businessMapping = {
      'user management': ['auth', 'login', 'register', 'profile', 'account'],
      'content management': ['post', 'page', 'article', 'content', 'editor'],
      'e-commerce': ['cart', 'checkout', 'payment', 'order', 'product', 'inventory'],
      'communication': ['email', 'notification', 'message', 'comment'],
      'analytics': ['tracking', 'analytics', 'report', 'dashboard'],
      'search': ['search', 'filter', 'query', 'index'],
      'media handling': ['upload', 'image', 'video', 'gallery', 'media'],
      'security': ['security', 'auth', 'permission', 'access', 'token'],
      'integration': ['api', 'webhook', 'integration', 'sync'],
      'performance': ['cache', 'optimization', 'performance', 'speed']
    };

    parsedChanges.files.forEach(file => {
      const filePath = file.path.toLowerCase();
      
      for (const [businessFunc, keywords] of Object.entries(businessMapping)) {
        if (keywords.some(keyword => filePath.includes(keyword))) {
          businessFunctions.add(businessFunc);
        }
      }
    });

    return Array.from(businessFunctions);
  }

  /**
   * Identify affected integrations
   */
  identifyAffectedIntegrations(parsedChanges) {
    const integrations = new Set();

    const integrationPatterns = [
      /api|endpoint|webhook/i,
      /integration|connector/i,
      /third.?party|external/i,
      /payment|stripe|paypal/i,
      /social|facebook|twitter|google/i,
      /email|smtp|mailgun/i,
      /analytics|google.?analytics|gtag/i,
      /cdn|cloudflare|aws/i
    ];

    parsedChanges.files.forEach(file => {
      const filePath = file.path;
      
      integrationPatterns.forEach(pattern => {
        if (pattern.test(filePath)) {
          integrations.add(this.extractIntegrationName(filePath, pattern));
        }
      });
    });

    return Array.from(integrations).filter(Boolean);
  }

  /**
   * Extract integration name from file path
   */
  extractIntegrationName(filePath, pattern) {
    const match = filePath.match(pattern);
    if (match) {
      return match[0].replace(/[._-]/g, ' ').trim();
    }
    return null;
  }

  /**
   * Identify affected user roles
   */
  identifyAffectedUserRoles(parsedChanges) {
    const roles = new Set();

    const rolePatterns = {
      'Administrator': [/admin|administrator|manage/i],
      'Editor': [/editor|edit|content/i],
      'Author': [/author|writer|creator/i],
      'Subscriber': [/subscriber|user|member/i],
      'Guest': [/guest|anonymous|public/i],
      'Customer': [/customer|buyer|shopper/i],
      'Vendor': [/vendor|seller|merchant/i]
    };

    parsedChanges.files.forEach(file => {
      const filePath = file.path;
      
      for (const [role, patterns] of Object.entries(rolePatterns)) {
        if (patterns.some(pattern => pattern.test(filePath))) {
          roles.add(role);
        }
      }
    });

    // Default roles if none detected
    if (roles.size === 0) {
      roles.add('All Users');
    }

    return Array.from(roles);
  }

  /**
   * Identify affected pages/routes
   */
  identifyAffectedPages(parsedChanges) {
    const pages = new Set();

    parsedChanges.files.forEach(file => {
      const filePath = file.path;
      
      // Extract page names from common patterns
      const pagePatterns = [
        /pages?\/([^\/]+)/i,
        /routes?\/([^\/]+)/i,
        /views?\/([^\/]+)/i,
        /templates?\/([^\/]+)/i
      ];

      pagePatterns.forEach(pattern => {
        const match = filePath.match(pattern);
        if (match) {
          const pageName = match[1].replace(/\.(php|js|jsx|ts|tsx|vue|html)$/i, '');
          pages.add(pageName.replace(/[-_]/g, ' '));
        }
      });
    });

    return Array.from(pages);
  }

  /**
   * Identify risk areas
   */
  identifyRiskAreas(parsedChanges) {
    const riskAreas = {
      high: new Set(),
      medium: new Set(),
      low: new Set()
    };

    parsedChanges.files.forEach(file => {
      const filePath = file.path;
      
      for (const [riskLevel, patterns] of Object.entries(this.riskIndicators)) {
        patterns.forEach(pattern => {
          if (pattern.test(filePath)) {
            riskAreas[riskLevel].add(this.extractRiskArea(filePath, pattern));
          }
        });
      }
    });

    return {
      high: Array.from(riskAreas.high).filter(Boolean),
      medium: Array.from(riskAreas.medium).filter(Boolean),
      low: Array.from(riskAreas.low).filter(Boolean)
    };
  }

  /**
   * Extract risk area from file path
   */
  extractRiskArea(filePath, pattern) {
    const match = filePath.match(pattern);
    if (match) {
      return match[0].replace(/[._-]/g, ' ').trim();
    }
    return path.basename(filePath, path.extname(filePath));
  }

  /**
   * Calculate testing priority
   */
  calculateTestingPriority(parsedChanges) {
    let priority = 0;

    // Factor in risk assessment
    if (parsedChanges.riskAssessment) {
      switch (parsedChanges.riskAssessment.level) {
        case 'high': priority += 3; break;
        case 'medium': priority += 2; break;
        case 'low': priority += 1; break;
      }
    }

    // Factor in change size
    const totalChanges = parsedChanges.stats?.totalAdditions + parsedChanges.stats?.totalDeletions || 0;
    if (totalChanges > 500) priority += 3;
    else if (totalChanges > 100) priority += 2;
    else priority += 1;

    // Factor in file categories
    const categories = parsedChanges.categories || {};
    if (categories.config?.length > 0) priority += 2;
    if (categories.database?.length > 0) priority += 3;
    if (categories.backend?.length > 0) priority += 2;

    if (priority >= 7) return 'critical';
    if (priority >= 5) return 'high';
    if (priority >= 3) return 'medium';
    return 'low';
  }

  /**
   * Check if changes are user-facing
   */
  isUserFacing(parsedChanges) {
    const userFacingCategories = ['frontend', 'assets'];
    const categories = parsedChanges.categories || {};
    
    return userFacingCategories.some(category => 
      categories[category] && categories[category].length > 0
    );
  }

  /**
   * Check if changes are backend-related
   */
  isBackendChange(parsedChanges) {
    const backendCategories = ['backend', 'database', 'config'];
    const categories = parsedChanges.categories || {};
    
    return backendCategories.some(category => 
      categories[category] && categories[category].length > 0
    );
  }

  /**
   * Check if changes are potentially breaking
   */
  isBreakingChange(parsedChanges) {
    const breakingIndicators = [
      /migration|schema/i,
      /config|environment/i,
      /api.*version|v\d+/i,
      /breaking|deprecated/i
    ];

    return parsedChanges.files.some(file => 
      breakingIndicators.some(pattern => pattern.test(file.path))
    ) || parsedChanges.stats?.deletedFiles > 0;
  }
}

module.exports = { ChangeAnalyzer };
