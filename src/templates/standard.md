# QA Test Plan: {{title}}

**Generated:** {{date}}  
**Risk Level:** {{risk_level}}  
**Priority:** {{priority}}

---

## 📋 Summary

**What Changed:** {{description}}

**Components Affected:** {{components}}

**Business Purpose:** {{purpose}}

**User Impact:** {{user_impact}}

**Files Modified:** {{files_count}} files

---

## ✅ Test Cases

{{test_cases}}

---

## 🔍 Edge Cases & Error Scenarios

{{edge_cases}}

---

## 🌐 Compatibility Testing

### Browser Testing
{{browser_tests}}

### Device Testing
{{device_tests}}

### User Role Testing
{{role_tests}}

---

## ⚡ Performance Testing

{{performance_tests}}

---

## 🔒 Security Testing

{{security_tests}}

---

## 📝 Notes

- Test on both staging and production environments
- Document any issues found during testing
- Verify fixes before marking items complete
- Consider accessibility testing for user-facing changes

**Generated by BranchQA** - AI-powered QA test case generator
