/**
 * GitHub Service for BranchQA
 * Handles GitHub API interactions and git operations
 */

const axios = require('axios');
const { simpleGit } = require('simple-git');
const fs = require('fs').promises;
const path = require('path');
const os = require('os');

class GitHubService {
  constructor(token) {
    this.token = token;
    this.api = axios.create({
      baseURL: 'https://api.github.com',
      headers: {
        'Authorization': `token ${token}`,
        'Accept': 'application/vnd.github.v3+json',
        'User-Agent': 'BranchQA/1.0.0'
      }
    });
    this.git = simpleGit();
  }

  /**
   * Get branch differences between two branches
   */
  async getBranchDiff(repository, baseBranch, headBranch) {
    try {
      // Get repository information
      const repoInfo = await this.getRepositoryInfo(repository);
      
      // Clone or update local repository
      const localRepoPath = await this.ensureLocalRepository(repository, repoInfo.clone_url);
      
      // Get the diff between branches
      const diffData = await this.getDiffData(localRepoPath, baseBranch, headBranch);
      
      // Get commit information
      const commits = await this.getCommitsBetweenBranches(repository, baseBranch, headBranch);
      
      // Get pull request information if available
      const pullRequest = await this.findPullRequest(repository, headBranch, baseBranch);
      
      return {
        repository: repoInfo,
        baseBranch,
        headBranch,
        diff: diffData,
        commits,
        pullRequest,
        stats: await this.getDiffStats(localRepoPath, baseBranch, headBranch)
      };
    } catch (error) {
      throw new Error(`Failed to get branch diff: ${error.message}`);
    }
  }

  /**
   * Get repository information from GitHub API
   */
  async getRepositoryInfo(repository) {
    try {
      const response = await this.api.get(`/repos/${repository}`);
      return response.data;
    } catch (error) {
      if (error.response?.status === 404) {
        throw new Error(`Repository ${repository} not found or not accessible`);
      }
      throw new Error(`Failed to fetch repository info: ${error.message}`);
    }
  }

  /**
   * Ensure local repository exists and is up to date
   */
  async ensureLocalRepository(repository, cloneUrl) {
    const repoDir = path.join(os.tmpdir(), 'branchqa-repos', repository.replace('/', '-'));
    
    try {
      // Check if repository already exists
      await fs.access(repoDir);
      
      // Repository exists, update it
      const git = simpleGit(repoDir);
      await git.fetch(['--all']);
      
      return repoDir;
    } catch (error) {
      // Repository doesn't exist, clone it
      await fs.mkdir(path.dirname(repoDir), { recursive: true });
      
      const git = simpleGit();
      await git.clone(cloneUrl, repoDir);
      
      return repoDir;
    }
  }

  /**
   * Get diff data between two branches
   */
  async getDiffData(repoPath, baseBranch, headBranch) {
    const git = simpleGit(repoPath);
    
    try {
      // Ensure we have the latest branches
      await git.checkout(baseBranch);
      await git.pull('origin', baseBranch);
      await git.checkout(headBranch);
      await git.pull('origin', headBranch);
      
      // Get the diff
      const diffSummary = await git.diffSummary([`${baseBranch}...${headBranch}`]);
      const diffText = await git.diff([`${baseBranch}...${headBranch}`]);
      
      return {
        summary: diffSummary,
        text: diffText,
        files: diffSummary.files
      };
    } catch (error) {
      throw new Error(`Failed to get diff data: ${error.message}`);
    }
  }

  /**
   * Get diff statistics
   */
  async getDiffStats(repoPath, baseBranch, headBranch) {
    const git = simpleGit(repoPath);
    
    try {
      const diffSummary = await git.diffSummary([`${baseBranch}...${headBranch}`]);
      
      return {
        filesChanged: diffSummary.files.length,
        insertions: diffSummary.insertions,
        deletions: diffSummary.deletions,
        totalChanges: diffSummary.insertions + diffSummary.deletions
      };
    } catch (error) {
      throw new Error(`Failed to get diff stats: ${error.message}`);
    }
  }

  /**
   * Get commits between two branches
   */
  async getCommitsBetweenBranches(repository, baseBranch, headBranch) {
    try {
      const response = await this.api.get(`/repos/${repository}/compare/${baseBranch}...${headBranch}`);
      return response.data.commits.map(commit => ({
        sha: commit.sha,
        message: commit.commit.message,
        author: commit.commit.author,
        date: commit.commit.author.date,
        url: commit.html_url
      }));
    } catch (error) {
      console.warn(`Could not fetch commits: ${error.message}`);
      return [];
    }
  }

  /**
   * Find pull request for the given branches
   */
  async findPullRequest(repository, headBranch, baseBranch) {
    try {
      const response = await this.api.get(`/repos/${repository}/pulls`, {
        params: {
          head: headBranch,
          base: baseBranch,
          state: 'all'
        }
      });
      
      if (response.data.length > 0) {
        const pr = response.data[0];
        return {
          number: pr.number,
          title: pr.title,
          body: pr.body,
          state: pr.state,
          url: pr.html_url,
          author: pr.user.login,
          created_at: pr.created_at,
          updated_at: pr.updated_at
        };
      }
      
      return null;
    } catch (error) {
      console.warn(`Could not fetch pull request: ${error.message}`);
      return null;
    }
  }

  /**
   * Validate GitHub token
   */
  async validateToken() {
    try {
      const response = await this.api.get('/user');
      return {
        valid: true,
        user: response.data.login,
        scopes: response.headers['x-oauth-scopes']?.split(', ') || []
      };
    } catch (error) {
      return {
        valid: false,
        error: error.message
      };
    }
  }
}

module.exports = { GitHubService };
