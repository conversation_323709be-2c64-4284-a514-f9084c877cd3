/**
 * AI Service for BranchQA
 * Handles OpenAI API interactions for contextual analysis
 */

const OpenAI = require('openai');

class AIService {
  constructor(apiKey) {
    this.openai = new OpenAI({
      apiKey: apiKey
    });
  }

  /**
   * Generate contextual analysis from code changes
   */
  async generateContext(parsedChanges, impactAnalysis, commits) {
    try {
      const prompt = this.buildContextPrompt(parsedChanges, impactAnalysis, commits);
      
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: this.getSystemPrompt()
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 2000
      });

      const analysis = response.choices[0].message.content;
      return this.parseContextualAnalysis(analysis);
    } catch (error) {
      throw new Error(`AI analysis failed: ${error.message}`);
    }
  }

  /**
   * Get system prompt for contextual analysis
   */
  getSystemPrompt() {
    return `You are an expert QA engineer and software analyst. Your task is to analyze code changes and provide structured insights for QA testing.

For each set of code changes, provide analysis in the following JSON format:

{
  "summary": {
    "title": "Brief, descriptive title of the changes",
    "description": "2-3 sentence overview of what was changed"
  },
  "what": {
    "components": ["List of affected components/features"],
    "files": ["Key files that were modified"],
    "scope": "Brief description of the scope of changes"
  },
  "why": {
    "purpose": "Business rationale for the changes",
    "problem_solved": "What problem this change addresses",
    "user_benefit": "How this benefits users"
  },
  "impact": {
    "user_workflows": ["Affected user workflows"],
    "admin_features": ["Affected admin/backend features"],
    "integrations": ["Affected integrations or APIs"],
    "performance": "Performance implications",
    "security": "Security implications"
  },
  "risk_assessment": {
    "level": "low|medium|high",
    "areas": ["Areas of highest risk"],
    "edge_cases": ["Potential edge cases to test"]
  }
}

Focus on practical, actionable insights that will help QA engineers create comprehensive test plans.`;
  }

  /**
   * Build the context prompt from changes and commits
   */
  buildContextPrompt(parsedChanges, impactAnalysis, commits) {
    let prompt = "Analyze the following code changes and provide structured insights:\n\n";

    // Add commit information
    if (commits && commits.length > 0) {
      prompt += "## Commit Messages:\n";
      commits.forEach(commit => {
        prompt += `- ${commit.message.split('\n')[0]} (${commit.author.name})\n`;
      });
      prompt += "\n";
    }

    // Add file changes
    if (parsedChanges && parsedChanges.files) {
      prompt += "## File Changes:\n";
      parsedChanges.files.forEach(file => {
        prompt += `- ${file.type}: ${file.path} (+${file.additions} -${file.deletions})\n`;
      });
      prompt += "\n";
    }

    // Add impact analysis
    if (impactAnalysis) {
      prompt += "## Impact Analysis:\n";
      prompt += `- Components affected: ${impactAnalysis.components?.join(', ') || 'Unknown'}\n`;
      prompt += `- User-facing changes: ${impactAnalysis.userFacing ? 'Yes' : 'No'}\n`;
      prompt += `- Backend changes: ${impactAnalysis.backend ? 'Yes' : 'No'}\n`;
      prompt += "\n";
    }

    // Add code snippets for context (limited)
    if (parsedChanges && parsedChanges.significantChanges) {
      prompt += "## Key Code Changes:\n";
      parsedChanges.significantChanges.slice(0, 3).forEach(change => {
        prompt += `### ${change.file}\n`;
        prompt += `\`\`\`\n${change.snippet}\n\`\`\`\n\n`;
      });
    }

    prompt += "Please provide your analysis in the specified JSON format.";

    return prompt;
  }

  /**
   * Parse the AI response into structured data
   */
  parseContextualAnalysis(analysis) {
    try {
      // Try to extract JSON from the response
      const jsonMatch = analysis.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      
      // If no JSON found, create a basic structure
      return this.createFallbackAnalysis(analysis);
    } catch (error) {
      console.warn('Failed to parse AI response as JSON, using fallback');
      return this.createFallbackAnalysis(analysis);
    }
  }

  /**
   * Create fallback analysis structure
   */
  createFallbackAnalysis(rawAnalysis) {
    return {
      summary: {
        title: "Code Changes Analysis",
        description: "Analysis of recent code changes for QA testing"
      },
      what: {
        components: ["Various components"],
        files: ["Multiple files"],
        scope: "Code modifications requiring testing"
      },
      why: {
        purpose: "Feature enhancement or bug fix",
        problem_solved: "Addressing user or system requirements",
        user_benefit: "Improved functionality or user experience"
      },
      impact: {
        user_workflows: ["User interface interactions"],
        admin_features: ["Administrative functions"],
        integrations: ["System integrations"],
        performance: "Potential performance impact",
        security: "Security considerations"
      },
      risk_assessment: {
        level: "medium",
        areas: ["User interface", "Data handling"],
        edge_cases: ["Boundary conditions", "Error scenarios"]
      },
      raw_analysis: rawAnalysis
    };
  }

  /**
   * Generate test scenarios based on analysis
   */
  async generateTestScenarios(contextualAnalysis, additionalContext = {}) {
    try {
      const prompt = this.buildTestScenariosPrompt(contextualAnalysis, additionalContext);
      
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: this.getTestScenariosSystemPrompt()
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.2,
        max_tokens: 3000
      });

      return response.choices[0].message.content;
    } catch (error) {
      throw new Error(`Test scenario generation failed: ${error.message}`);
    }
  }

  /**
   * Get system prompt for test scenarios
   */
  getTestScenariosSystemPrompt() {
    return `You are an expert QA engineer specializing in comprehensive test case design. Generate detailed, actionable test scenarios based on code change analysis.

Create test scenarios that include:
1. Clear test case titles
2. Specific routes/URLs to test
3. User roles to consider
4. Step-by-step testing instructions
5. Expected results
6. Edge cases and error scenarios
7. Browser/device compatibility considerations

Format the output as structured Markdown that can be used directly as a QA checklist.`;
  }

  /**
   * Build test scenarios prompt
   */
  buildTestScenariosPrompt(contextualAnalysis, additionalContext) {
    let prompt = "Based on the following code change analysis, generate comprehensive test scenarios:\n\n";
    
    prompt += `## Change Summary:\n${contextualAnalysis.summary.description}\n\n`;
    prompt += `## Components Affected:\n${contextualAnalysis.what.components.join(', ')}\n\n`;
    prompt += `## User Impact:\n${contextualAnalysis.impact.user_workflows.join(', ')}\n\n`;
    prompt += `## Risk Level:\n${contextualAnalysis.risk_assessment.level}\n\n`;
    
    if (additionalContext.projectType) {
      prompt += `## Project Type:\n${additionalContext.projectType}\n\n`;
    }
    
    prompt += "Generate detailed test scenarios in Markdown checklist format.";
    
    return prompt;
  }
}

module.exports = { AIService };
